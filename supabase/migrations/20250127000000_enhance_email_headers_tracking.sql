-- Enhance email_messages table with Message-ID tracking for email thread correlation
-- This migration adds the critical Message-ID header field to enable tracking between
-- outgoing RFQ emails and incoming Gmail responses

-- Add Message-ID column to email_messages table for email tracking
ALTER TABLE public.email_messages
ADD COLUMN message_id TEXT;

-- Add comment for the new column
COMMENT ON COLUMN public.email_messages.message_id IS 'Message-ID header value from Gmail API for email tracking and thread correlation with outgoing RFQ emails';

-- Create index for efficient Message-ID lookups (critical for tracking outgoing emails)
CREATE INDEX IF NOT EXISTS idx_email_messages_message_id ON public.email_messages (message_id);

-- Index for In-Reply-To lookups (existing column, adding index for performance)
CREATE INDEX IF NOT EXISTS idx_email_messages_in_reply_to_lookup ON public.email_messages (in_reply_to);

-- Fix Supabase linter suggestions: Add indexes for unindexed foreign keys

-- Email settings table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_email_settings_created_by ON public.email_settings (created_by);
CREATE INDEX IF NOT EXISTS idx_email_settings_updated_by ON public.email_settings (updated_by);

-- Provider contacts table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_provider_contacts_created_by ON public.provider_contacts (created_by);
CREATE INDEX IF NOT EXISTS idx_provider_contacts_updated_by ON public.provider_contacts (updated_by);

-- Provider routes table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_provider_routes_created_by ON public.provider_routes (created_by);
CREATE INDEX IF NOT EXISTS idx_provider_routes_updated_by ON public.provider_routes (updated_by);

-- Providers table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_providers_created_by ON public.providers (created_by);
CREATE INDEX IF NOT EXISTS idx_providers_updated_by ON public.providers (updated_by);

-- RFQ bids table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfq_bids_original_email_id ON public.rfq_bids (original_email_id);

-- RFQ incoming emails table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfq_incoming_emails_extracted_bid_id ON public.rfq_incoming_emails (extracted_bid_id);

-- RFQs table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfqs_cargo_type_id ON public.rfqs (cargo_type_id);
CREATE INDEX IF NOT EXISTS idx_rfqs_equipment_type_id ON public.rfqs (equipment_type_id);

-- User roles table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON public.user_roles (role_id);

-- Remove unused index that was flagged by Supabase linter
DROP INDEX IF EXISTS idx_rfqs_created_by;