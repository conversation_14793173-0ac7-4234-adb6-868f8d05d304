-- Enhance email_messages table with Message-ID tracking for email thread correlation
-- This migration adds the critical Message-ID header field to enable tracking between
-- outgoing RFQ emails and incoming Gmail responses

-- Add Message-ID column to email_messages table for email tracking
ALTER TABLE public.email_messages
ADD COLUMN message_id TEXT;

-- Add comment for the new column
COMMENT ON COLUMN public.email_messages.message_id IS 'Message-ID header value from Gmail API for email tracking and thread correlation with outgoing RFQ emails';

-- Create index for efficient Message-ID lookups (critical for tracking outgoing emails)
CREATE INDEX IF NOT EXISTS idx_email_messages_message_id ON public.email_messages (message_id);

-- Index for In-Reply-To lookups (existing column, adding index for performance)
CREATE INDEX IF NOT EXISTS idx_email_messages_in_reply_to_lookup ON public.email_messages (in_reply_to);

-- Create a function to extract Message-ID from various header formats
-- This handles both <message-id@domain> and message-id@domain formats
CREATE OR REPLACE FUNCTION extract_clean_message_id(message_id_header TEXT)
RETURNS TEXT AS $$
BEGIN
    IF message_id_header IS NULL OR message_id_header = '' THEN
        RETURN NULL;
    END IF;

    -- Remove angle brackets if present
    RETURN TRIM(BOTH '<>' FROM TRIM(message_id_header));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to find related emails by Message-ID
-- This function helps correlate outgoing and incoming emails
CREATE OR REPLACE FUNCTION find_related_emails_by_message_id(target_message_id TEXT)
RETURNS TABLE (
    email_id UUID,
    email_type TEXT,
    message_id TEXT,
    in_reply_to TEXT,
    subject TEXT,
    date_received TIMESTAMPTZ
) AS $$
BEGIN
    -- Clean the target Message-ID
    target_message_id := extract_clean_message_id(target_message_id);

    IF target_message_id IS NULL THEN
        RETURN;
    END IF;

    -- Find emails that reference this Message-ID
    RETURN QUERY
    SELECT
        em.id as email_id,
        'incoming'::TEXT as email_type,
        extract_clean_message_id(em.message_id) as message_id,
        extract_clean_message_id(em.in_reply_to) as in_reply_to,
        em.subject,
        em.date_received
    FROM public.email_messages em
    WHERE
        extract_clean_message_id(em.in_reply_to) = target_message_id
        OR extract_clean_message_id(em.message_id) = target_message_id

    UNION ALL

    -- Find outgoing emails with this Message-ID
    SELECT
        roe.id as email_id,
        'outgoing'::TEXT as email_type,
        extract_clean_message_id(roe.message_id) as message_id,
        NULL::TEXT as in_reply_to,
        roe.subject,
        roe.sent_at as date_received
    FROM public.rfq_outgoing_emails roe
    WHERE extract_clean_message_id(roe.message_id) = target_message_id

    ORDER BY date_received DESC;
END;
$$ LANGUAGE plpgsql;
