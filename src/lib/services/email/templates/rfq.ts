import { baseTemplate, baseTextTemplate } from "./base";
import { marked } from "marked";

/**
 * Convert markdown to HTML for email content
 * Ensures all content is left-aligned for a more personal email appearance
 */
function markdownToHtml(markdown: string): string {
  // Parse markdown to HTML
  let html = marked.parse(markdown) as string;

  // Add left alignment to common HTML elements that might be generated from markdown
  // This ensures that all content is left-aligned in email clients
  html = html
    // Add text-align: left to paragraphs
    .replace(/<p>/g, '<p style="text-align: left;">')
    // Add text-align: left to headings
    .replace(/<h([1-6])>/g, '<h$1 style="text-align: left;">')
    // Add text-align: left to list items
    .replace(/<li>/g, '<li style="text-align: left;">')
    // Add text-align: left to tables
    .replace(/<table>/g, '<table style="text-align: left;">')
    // Add text-align: left to table cells
    .replace(/<td>/g, '<td style="text-align: left;">');

  return html;
}

/**
 * Convert markdown to plain text
 * This is a simple implementation that handles basic markdown
 */
function markdownToText(markdown: string): string {
  // Replace common markdown elements with plain text equivalents
  return markdown
    .replace(/#{1,6}\s+(.+)/g, '$1') // Headers
    .replace(/\*\*(.+?)\*\*/g, '$1') // Bold
    .replace(/\*(.+?)\*/g, '$1') // Italic
    .replace(/\[(.+?)\]\((.+?)\)/g, '$1 ($2)') // Links
    .replace(/^\s*[-*+]\s+(.+)/gm, '- $1') // List items
    .replace(/^\s*\d+\.\s+(.+)/gm, '$1') // Numbered list items
    .replace(/`(.+?)`/g, '$1') // Inline code
    .replace(/```[\s\S]+?```/g, '') // Code blocks
    .trim();
}

/**
 * RFQ email template interface
 */
export interface RFQTemplateResult {
  html: string;
  text: string;
}

/**
 * RFQ email template - Redesigned to be more personal and conversational
 * Now returns both HTML and plain text versions
 */
export function rfqTemplate(rfqDetails: {
  rfqNumber: string;
  origin: string;
  originCountry: string;
  destination: string;
  destinationCountry: string;
  body: string;
  cargoTypes?: string;
  companyName?: string;
  companyLogoUrl?: string | null;
  emailFooterText?: string | null;
  userName?: string;
  userPhone?: string;
}): RFQTemplateResult {
  const {
    rfqNumber,
    origin,
    originCountry,
    destination,
    destinationCountry,
    body,
    cargoTypes,
    companyName,
    companyLogoUrl,
    emailFooterText,
    userName,
    userPhone,
  } = rfqDetails;

  // Create a more subtle reference to the RFQ number for tracking
  const route = `${origin}, ${originCountry} to ${destination}, ${destinationCountry}`;
  const title = `Quote Request: ${route}`;

  // Convert markdown body to HTML for HTML version
  const htmlBody = markdownToHtml(body);

  // Convert markdown body to plain text for text version
  const textBody = markdownToText(body);

  // Create the email content (HTML version)
  // The template now only provides structure and styling, not predefined message text
  // The entire message content is passed through the body parameter
  const cargoTypesHtml = cargoTypes ? `<p style="text-align: left; margin: 10px 0; color: #555; font-size: 14px;"><strong>Cargo Type:</strong> ${cargoTypes}</p>` : '';

  const htmlContent = `<div style="text-align: left;">
${cargoTypesHtml}
${htmlBody}
</div>

<p style="font-size: 11px; color: #777; margin-top: 30px; text-align: left;">Reference: ${rfqNumber}</p>`;

  // Create a plain text version of the email content
  // The template now only provides structure, not predefined message text
  const cargoTypesText = cargoTypes ? `Cargo Type: ${cargoTypes}\n\n` : '';

  const textContent = `${cargoTypesText}${textBody}

Reference: ${rfqNumber}`;

  return {
    html: baseTemplate(htmlContent, {
      title,
      companyName,
      companyLogoUrl,
      emailFooterText,
      userName,
      userPhone
    }),
    text: baseTextTemplate(textContent, {
      title,
      companyName,
      emailFooterText,
      userName,
      userPhone
    })
  };
}
